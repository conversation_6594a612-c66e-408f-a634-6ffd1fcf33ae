/**
 * Centralized form utilities for arbeidskontrakt generator
 * Eliminates repeated form handling patterns across step components
 */

import { ContractFormData } from '../types';

// Generic form field update handler
export const createFormFieldUpdater = (
  updateFormData: (updates: Partial<ContractFormData>) => void
) => {
  return (field: keyof ContractFormData, value: string | number | boolean) => {
    updateFormData({ [field]: value });
  };
};

// Date formatting utility for consistent date display
export const formatContractDate = (dateString: string): string => {
  if (!dateString) return '__.__.__';
  const date = new Date(dateString);
  return date.toLocaleDateString('no-NO', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Validation helpers for form fields
export const validateRequiredField = (value: string | number): boolean => {
  if (typeof value === 'string') {
    return value.trim().length > 0;
  }
  return value > 0;
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateAccountNumber = (accountNumber: string): boolean => {
  // Norwegian account number format: XXXX.XX.XXXXX
  const accountRegex = /^\d{4}\.\d{2}\.\d{5}$/;
  return accountRegex.test(accountNumber);
};

// Form step validation
export const validateBasicInfoStep = (formData: ContractFormData): boolean => {
  return (
    validateRequiredField(formData.employeeName) &&
    validateRequiredField(formData.employeeAddress) &&
    validateRequiredField(formData.employeeBirthDate) &&
    validateRequiredField(formData.startDate) &&
    validateRequiredField(formData.position) &&
    validateRequiredField(formData.hourlyRate) &&
    validateRequiredField(formData.accountNumber)
  );
};

export const validateAdvancedSettingsStep = (formData: ContractFormData): boolean => {
  return (
    validateRequiredField(formData.companyName) &&
    validateRequiredField(formData.companyOrgNumber) &&
    validateRequiredField(formData.companyAddress) &&
    validateRequiredField(formData.workingHoursPerWeek) &&
    validateRequiredField(formData.workingTime)
  );
};

// Employment type change handler with side effects
export const handleEmploymentTypeChange = (
  value: string,
  updateFormData: (updates: Partial<ContractFormData>) => void
) => {
  const isTemporary = value === 'midlertidig';
  updateFormData({
    employmentType: value as 'fast' | 'midlertidig',
    isTemporary,
    // Clear temporary fields if switching to permanent
    ...(isTemporary ? {} : { temporaryEndDate: '', temporaryReason: '' })
  });
};

// Probation period change handler with side effects
export const handleProbationPeriodChange = (
  enabled: boolean,
  updateFormData: (updates: Partial<ContractFormData>) => void
) => {
  updateFormData({
    probationPeriod: enabled,
    // Reset probation months if disabling
    ...(enabled ? {} : { probationMonths: 6 })
  });
};

// Own tools change handler with side effects
export const handleOwnToolsChange = (
  enabled: boolean,
  updateFormData: (updates: Partial<ContractFormData>) => void
) => {
  updateFormData({
    ownTools: enabled,
    // Clear tool allowance if not using own tools
    ...(enabled ? {} : { toolAllowance: '' })
  });
};
