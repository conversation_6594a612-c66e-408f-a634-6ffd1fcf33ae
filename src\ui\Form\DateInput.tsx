import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface DateInputProps {
  label?: string;
  error?: string;
  helper?: string;
  value?: string; // ISO date string (YYYY-MM-DD)
  onChange?: (e: { target: { value: string } }) => void;
  placeholder?: string;
  required?: boolean;
  name?: string;
  id?: string;
  className?: string;
}

const DateInput = React.forwardRef<HTMLInputElement, DateInputProps>(
  ({ label, error, helper, value, onChange, placeholder, required, name, id, className, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState('');
    const inputId = id || name;

    // Convert ISO date to display format (dd.mm.yyyy)
    const formatDateForDisplay = (isoDate: string): string => {
      if (!isoDate) return '';
      try {
        const date = new Date(isoDate);
        if (isNaN(date.getTime())) return '';
        
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        
        return `${day}.${month}.${year}`;
      } catch {
        return '';
      }
    };

    // Convert display format (dd.mm.yyyy) to ISO date
    const parseDisplayDate = (displayDate: string): string => {
      if (!displayDate) return '';
      
      // Remove any non-digit characters except dots
      const cleaned = displayDate.replace(/[^\d.]/g, '');
      
      // Check if it matches dd.mm.yyyy pattern
      const match = cleaned.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
      if (!match) return '';
      
      const [, day, month, year] = match;
      const dayNum = parseInt(day, 10);
      const monthNum = parseInt(month, 10);
      const yearNum = parseInt(year, 10);
      
      // Basic validation
      if (dayNum < 1 || dayNum > 31 || monthNum < 1 || monthNum > 12) {
        return '';
      }
      
      try {
        // Create date and validate it's real
        const date = new Date(yearNum, monthNum - 1, dayNum);
        if (date.getFullYear() !== yearNum || 
            date.getMonth() !== monthNum - 1 || 
            date.getDate() !== dayNum) {
          return '';
        }
        
        // Return ISO format
        return `${yearNum}-${monthNum.toString().padStart(2, '0')}-${dayNum.toString().padStart(2, '0')}`;
      } catch {
        return '';
      }
    };

    // Update display value when prop value changes
    useEffect(() => {
      setDisplayValue(formatDateForDisplay(value || ''));
    }, [value]);

    // Handle input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let inputValue = e.target.value;
      
      // Auto-format as user types
      const digitsOnly = inputValue.replace(/\D/g, '');
      
      let formatted = '';
      if (digitsOnly.length >= 1) {
        formatted = digitsOnly.substring(0, 2);
      }
      if (digitsOnly.length >= 3) {
        formatted += '.' + digitsOnly.substring(2, 4);
      }
      if (digitsOnly.length >= 5) {
        formatted += '.' + digitsOnly.substring(4, 8);
      }
      
      setDisplayValue(formatted);
      
      // If we have a complete date, try to parse and notify parent
      if (formatted.length === 10) {
        const isoDate = parseDisplayDate(formatted);
        if (isoDate && onChange) {
          onChange({ target: { value: isoDate } });
        }
      } else if (formatted === '' && onChange) {
        // Clear the value if input is empty
        onChange({ target: { value: '' } });
      }
    };

    // Handle blur to validate final input
    const handleBlur = () => {
      if (displayValue.length === 10) {
        const isoDate = parseDisplayDate(displayValue);
        if (isoDate && onChange) {
          onChange({ target: { value: isoDate } });
          // Update display to ensure consistent formatting
          setDisplayValue(formatDateForDisplay(isoDate));
        }
      }
    };

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <input
          ref={ref}
          id={inputId}
          name={name}
          type="text"
          value={displayValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          maxLength={10}
          className={cn(
            'w-full rounded-md shadow-sm transition-colors duration-200',
            'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
            'px-3 py-2 text-sm',
            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            className
          )}
          {...props}
        />

        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

DateInput.displayName = 'DateInput';

export { DateInput };
export default DateInput;
