/**
 * Type definitions for the arbeidskontrakt generator
 * Isolated from main site types to prevent coupling
 */

import { getCompanyInfoForContract } from '../constants/company';

export interface ContractFormData {
  // Basic Info - Step 1
  employeeName: string;
  employeeAddress: string;
  employeeBirthDate: string;
  startDate: string;
  position: string;
  hourlyRate: number;
  accountNumber: string;
  employmentType: "fast" | "midlertidig";
  isTemporary: boolean;
  temporaryEndDate: string;
  temporaryReason: string;
  probationPeriod: boolean;
  probationMonths: number;
  ownTools: boolean;

  // Advanced Settings - Step 2
  companyName: string;
  companyOrgNumber: string;
  companyAddress: string;
  workingHoursPerWeek: number;
  workingTime: string;
  breakTime: string;
  overtimeRate: number;
  paymentDay: number;
  toolAllowance: string;
  travelAllowance: string;
  pensionProvider: string;
  pensionOrgNumber: string;
  insuranceProvider: string;
  insuranceOrgNumber: string;
  noticePeriod: string;
  contractDuration: string;
  notificationRules: string;
}

export interface ContractStepProps {
  formData: ContractFormData;
  updateFormData: (updates: Partial<ContractFormData>) => void;
  onNext?: () => void;
  onPrev?: () => void;
}

// Create initial form data using centralized company information
const companyDefaults = getCompanyInfoForContract();

export const initialContractFormData: ContractFormData = {
  // Basic Info
  employeeName: "",
  employeeAddress: "",
  employeeBirthDate: "01.01.1950",
  startDate: "",
  position: "",
  hourlyRate: companyDefaults.defaultHourlyRate,
  accountNumber: "",
  employmentType: "midlertidig",
  isTemporary: true,
  temporaryEndDate: "",
  temporaryReason: companyDefaults.defaultTemporaryReason,
  probationPeriod: true,
  probationMonths: companyDefaults.defaultProbationMonths,
  ownTools: false,

  // Advanced Settings - using centralized company data
  companyName: companyDefaults.companyName,
  companyOrgNumber: companyDefaults.companyOrgNumber,
  companyAddress: companyDefaults.companyAddress,
  workingHoursPerWeek: companyDefaults.workingHoursPerWeek,
  workingTime: companyDefaults.workingTime,
  breakTime: companyDefaults.breakTime,
  overtimeRate: companyDefaults.overtimeRate,
  paymentDay: companyDefaults.paymentDay,
  toolAllowance: companyDefaults.toolAllowance,
  travelAllowance: companyDefaults.travelAllowance,
  pensionProvider: companyDefaults.pensionProvider,
  pensionOrgNumber: companyDefaults.pensionOrgNumber,
  insuranceProvider: companyDefaults.insuranceProvider,
  insuranceOrgNumber: companyDefaults.insuranceOrgNumber,
  noticePeriod: companyDefaults.noticePeriod,
  contractDuration: "",
  notificationRules: companyDefaults.notificationRules,
};

// Position options for the dropdown
export const positionOptions = [
  { value: "anleggsgartner", label: "Anleggsgartner" },
  { value: "grunnarbeider", label: "Grunnarbeider" },
  { value: "maskinoperator", label: "Maskinoperatør" },
  { value: "hjelpearbeider", label: "Hjelpearbeider" },
  { value: "prosjektleder", label: "Prosjektleder" },
  { value: "annet", label: "Annet" }
];

// Employment type options
export const employmentTypeOptions = [
  { value: "fast", label: "Fast ansettelse" },
  { value: "midlertidig", label: "Midlertidig ansettelse" }
];

// Probation period options
export const probationMonthsOptions = [
  { value: "1", label: "1 måned" },
  { value: "2", label: "2 måneder" },
  { value: "3", label: "3 måneder" },
  { value: "4", label: "4 måneder" },
  { value: "5", label: "5 måneder" },
  { value: "6", label: "6 måneder" }
];

// Payment day options
export const paymentDayOptions = [
  { value: "1", label: "1. hver måned" },
  { value: "5", label: "5. hver måned" },
  { value: "10", label: "10. hver måned" },
  { value: "15", label: "15. hver måned" },
  { value: "20", label: "20. hver måned" },
  { value: "25", label: "25. hver måned" }
];

// Contract generation types
export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  version: string;
  lastUpdated: string;
}

export interface GeneratedContract {
  id: string;
  employeeName: string;
  position: string;
  generatedDate: string;
  contractData: ContractFormData;
  pdfUrl?: string;
  status: "draft" | "generated" | "signed";
}
